name: emartdriver
description: A new Flutter application.
publish_to: "none" # Remove this line if you wish to publish to pub.dev
version: 1.2.7+22

environment:
  sdk: ">=3.0.5 <4.0.0"

dependencies:
  audioplayers: ^6.0.0
  bottom_picker: ^2.8.0
  cached_network_image: ^3.4.1
  cloud_firestore: ^5.6.9
  cloud_functions: ^5.5.2
  crypto: ^3.0.6
  cupertino_icons: ^1.0.8
  dotted_border: ^2.1.0
  easy_localization: ^3.0.7+1
  firebase_app_check: ^0.3.2+7
  firebase_auth: ^5.6.0
  firebase_core: ^3.14.0
  firebase_database: ^11.3.7
  firebase_messaging: ^15.2.7
  firebase_storage: ^12.4.7
  flutter:
    sdk: flutter
  flutter_dash: ^1.0.0
  flutter_easyloading: ^3.0.5
  flutter_email_sender: ^6.0.3

  flutter_html: any
  flutter_launcher_icons: ^0.14.3
  flutter_local_notifications: ^19.0.0
  flutter_localizations:
    sdk: flutter
  flutter_map: ^8.1.1
  # flutter_osm_plugin: ^1.3.3+1  # Removed due to iOS build compatibility issues
  flutter_otp_text_field: ^1.2.0
  flutter_polyline_points: ^2.1.0
  flutter_svg: ^2.0.10+1
  flutter_widget_from_html: ^0.15.2
  geocoding: ^3.0.0
  geolocator: ^14.0.1
  get: ^4.6.6
  google_fonts: ^6.2.1
  google_maps_flutter: ^2.9.0
  google_maps_flutter_ios: ^2.15.0
  # Temporarily commenting out to fix build issues
  # google_maps_place_picker_mb: ^3.1.2

  http: ^1.2.2
  image_picker: ^1.1.2
  intl: ^0.20.2
  intl_phone_number_input: ^0.7.4
  location: ^6.0.2
  mailer: ^6.2.0
  map_launcher: ^3.5.0
  osm_nominatim: ^3.0.1
  package_info_plus: ^8.0.3
  path_provider: ^2.1.4
  photo_view: ^0.15.0
  pin_code_fields: ^8.0.1
  provider: ^6.1.2
  # razorpay_flutter: ^1.3.7
  share_plus: ^10.1.2
  shared_preferences: ^2.5.3
  shorebird_code_push: ^2.0.4
  sign_in_with_apple: ^6.1.3
  syncfusion_flutter_datepicker: ^29.2.5
  the_apple_sign_in: ^1.1.1
  url_launcher: ^6.3.1
  uuid: ^4.5.1
  video_compress: ^3.1.3
  video_player: ^2.9.2
  webview_flutter: ^4.10.0
  webview_flutter_android: ^4.3.4
  flutter_lints: ^2.0.0
  mask_text_input_formatter: ^2.9.0
  google_api_availability: ^5.0.1

  rxdart: any
  app_settings: ^6.1.1
  battery_plus: ^6.2.2
  workmanager: ^0.7.0

dependency_overrides:
  js: ^0.7.1

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/app_logo.png"

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/images/talisologo.png
    - assets/translations/
    - assets/fonts/
    - assets/flags/
    - assets/audio/
    - shorebird.yaml

  fonts:
    - family: RadioCanadaBig-Bold
      fonts:
        - asset: assets/fonts/RadioCanadaBig-Bold.ttf
    - family: Metropolis-Medium
      fonts:
        - asset: assets/fonts/RadioCanadaBig-Medium.ttf
    - family: RadioCanadaBig-Regular
      fonts:
        - asset: assets/fonts/RadioCanadaBig-Regular.ttf
    - family: RadioCanadaBig-SemiBold
      fonts:
        - asset: assets/fonts/RadioCanadaBig-SemiBold.ttf
